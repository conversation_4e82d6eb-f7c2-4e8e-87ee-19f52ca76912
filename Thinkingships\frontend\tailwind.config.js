/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#4A99F8',         // for buttons, highlights
        'primary-dark': '#0A06F4',  // for hover states
        secondary: '#76D6FC',       // for secondary buttons
        surface: '#F8F9FA',         // background
        'accent-pink': '#D99FE9',   // for tags, badges
        'accent-purple': '#CDAEEC', // for card/post backgrounds
        info: '#48CAE4',            // for info chips
      },
    },
  },
  plugins: ["@tailwindcss/typography", "daisyui"],
}
